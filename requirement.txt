请搜索网络列出一些小说的推荐需要有书名以及简介:
（1）在D:\git\scriptProject\book-list\list.txt中列出100本常规的推荐书
（2）在D:\git\scriptProject\book-list\list-back.txt中列出100本比较小众的推荐书

检查list1和list2下面内容：
（1）list中是否有重复的书名，如果有则删除重复的书名。
（2）搜索网络确认金句摘要的内容是不是书中确实存在的语句，如果没有则替换。
（3）你觉得书中有没有更加值得推荐的金句摘要，有的话请替换成新的金句摘要。


请完善D:\git\script-booklist\book-list\list1.txt：
金句摘要有点短了，最好不要少于50字，对于字数不足的金句摘要，请搜索网络来使用原书的语句来扩充这句话，如果一句的字数不够，可以写两句。


请完善文件 `D:\git\script-booklist\book-list\list1.txt` 中的金句摘要内容：

**任务要求：**
1. 检查文件中每条金句摘要的字数
2. 对于字数少于50字的金句摘要，需要进行扩充
3. 扩充方法：
   - 使用网络搜索功能查找原书中的相关语句
   - 必须使用原书的原文来扩充现有摘要
   - 如果单句扩充后仍不足50字，可以添加第二句相关内容
   - 第二句也必须使用原书的原文